"use client";
import React, { useState, useEffect, useMemo } from "react";
import { toast } from "sonner";
import apiClient from "@/lib/apiClient";
import { Button } from "../ui/button";
import { ChevronLeft, ChevronRight, Sessions, Users, Network } from "@/components/icons/list";
import { useSearchParams, useRouter } from "next/navigation";
import { DepartmentStats, PackageInfo } from "@/types/interface-type";
import {
  <PERSON><PERSON><PERSON>,
  LineChart,
  Line,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  <PERSON>Chart,
  Pie,
  Cell,
} from "recharts";

// Simplified interfaces
interface BarChartData {
  name: string;
  download: number;
  upload: number;
}

export interface LineChartData {
  timestamp: string;
  totalUsage: number;
  count: number;
}

export interface DepartmentOverviewData {
  departmentName: string;
  staffUsers: number;
  guestUsers: number;
  staffUsage: number; // in GB
  guestUsage: number; // in GB
  totalUsage: number; // in GB
  totalUsers: number;
}

interface ActiveStat extends DepartmentStats {
  inputMbps: number;
  outputMbps: number;
  count: number;
  recordId: string;
  created_at: string;
}

// Utility functions
const getDefaultDates = () => {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  const today = new Date();
  return {
    start: yesterday.toISOString().split("T")[0],
    end: today.toISOString().split("T")[0]
  };
};

// Utility functions for filling missing time periods
const generateDateRange = (startDateStr: string, endDateStr: string): string[] => {
  const start = new Date(startDateStr);
  const end = new Date(endDateStr);
  const dates: string[] = [];

  while (start <= end) {
    dates.push(new Date(start).toISOString().split("T")[0]); // "YYYY-MM-DD"
    start.setDate(start.getDate() + 1);
  }

  return dates;
};

const generateHourlyRange = (startDateStr: string, endDateStr: string): string[] => {
  const start = new Date(`${startDateStr} 00:00:00`);
  const end = new Date(`${endDateStr} 23:59:59`);
  const hours: string[] = [];

  const current = new Date(start);
  while (current <= end) {
    hours.push(current.toISOString());
    current.setHours(current.getHours() + 1);
  }

  return hours;
};

const fillMissingHours = (
  chartData: LineChartData[],
  startDate: string,
  endDate: string
): LineChartData[] => {
  const hourlyRange = generateHourlyRange(startDate, endDate);

  return hourlyRange.map((hour) => {
    const existingData = chartData.find((d) => d.timestamp === hour);
    return existingData || {
      timestamp: hour,
      totalUsage: 0,
      count: 0
    };
  });
};

const fillMissingDates = (
  chartData: LineChartData[],
  startDate: string,
  endDate: string
): LineChartData[] => {
  return generateDateRange(startDate, endDate).map((day) => {
    const dayEntries = chartData.filter((d) => d.timestamp.startsWith(day));
    const maxCount = dayEntries.length ? Math.max(...dayEntries.map((d) => d.count)) : 0;
    const totalUsage = dayEntries.reduce((sum, d) => sum + d.totalUsage, 0);

    return {
      timestamp: day,
      totalUsage: parseFloat(totalUsage.toFixed(2)),
      count: maxCount,
    };
  });
};

// Simplified chart components
const AnalyticsBarChart: React.FC<{ data: BarChartData[] }> = ({ data }) => {
  const filteredChartData = (data || [])
    .map((item: BarChartData) => ({
      name: item.name,
      total: (item.download || 0) + (item.upload || 0),
    }))
    .filter((item: { name: string; total: number }) => item.total > 0);

  const chartData = filteredChartData.length
    ? filteredChartData
    : [{ name: "No data", total: 0 }];

  return (
    <ResponsiveContainer width="100%" height="100%">
      <section><header className="text-center">Top Users by Bandwidth Usage (GB)</header></section>
      <BarChart
        data={chartData}
        layout="vertical"
        margin={{ top: 10, right: 20, left: 20, bottom: 10 }}
        barCategoryGap={25}
      >
        <CartesianGrid strokeDasharray="3 3" />
        <YAxis dataKey="name" type="category" hide />
        <XAxis type="number" />
        <Tooltip />
        <Legend verticalAlign="top" align="right" layout="horizontal" wrapperStyle={{ top: 0, right: 0 }} />
        <Bar
          dataKey="total"
          fill="#0ea516ff"
          radius={[0, 30, 30, 0]}
          label={({ x, y, height, name }) => {
            if (!name) return null;
            return (
              <text
                x={x + 15}
                y={y + height / 2.5}
                fill="black"
                fontSize={12}
                alignmentBaseline="middle"
                textAnchor="start"
              >
                {name}
              </text>
            );
          }}
        />
      </BarChart>
    </ResponsiveContainer>
  );
};



const DepartmentBarChart: React.FC<{ data: BarChartData[] }> = ({ data }) => {
  const filteredChartData = (data || []).filter(
    (item: BarChartData) => item.download > 0 || item.upload > 0
  );

  const chartData = filteredChartData.length
    ? filteredChartData
    : [{ name: "No data", download: 0, upload: 0 }];

  return (
    <ResponsiveContainer width="100%" height="100%">
      <section><header className="text-center">Top Departments by Bandwidth Usage (GB)</header></section>
      <BarChart
        data={chartData}
        layout="horizontal"
        margin={{ top: 1, right: 5, left: 5, bottom: 10 }}
      >
        <CartesianGrid strokeDasharray="0 0" />
        <XAxis dataKey="name" type="category" />
        <YAxis type="number" />
        <Tooltip />
        <Legend verticalAlign="top" align="right" layout="horizontal" wrapperStyle={{ top: 0, right: 0 }} />
        <Bar
          dataKey="download"
          fill="#0d9614ff"
        // radius={[0, 30, 30, 0]}
        />
        <Bar
          dataKey="upload"
          fill="#dc2626"
          position="inside"
        // radius={[0, 30, 30, 0]}
        />
      </BarChart>
    </ResponsiveContainer>
  );
};

const COLORS = ["#4ade80", "#22d3ee", "#facc15", "#f97316", "#ec4899", "#94a3b8"];

// Session Duration Analysis Chart
const SessionDurationChart: React.FC<{ data: DepartmentStats[] }> = ({ data }) => {
  const durationData = useMemo(() => {
    const completedSessions = data.filter(stat => stat.acctstoptime);
    const durationRanges = {
      "0-15m": 0,
      "15-30m": 0,
      "30-60m": 0,
      "1-2h": 0,
      "2-4h": 0,
      "4h+": 0
    };

    completedSessions.forEach(stat => {
      const start = new Date(stat.acctstarttime).getTime();
      const stop = new Date(stat.acctstoptime).getTime();
      const durationMinutes = (stop - start) / (1000 * 60);

      if (durationMinutes <= 15) durationRanges["0-15m"]++;
      else if (durationMinutes <= 30) durationRanges["15-30m"]++;
      else if (durationMinutes <= 60) durationRanges["30-60m"]++;
      else if (durationMinutes <= 120) durationRanges["1-2h"]++;
      else if (durationMinutes <= 240) durationRanges["2-4h"]++;
      else durationRanges["4h+"]++;
    });

    return Object.entries(durationRanges).map(([range, count]) => ({
      range,
      count,
      percentage: completedSessions.length > 0 ? ((count / completedSessions.length) * 100).toFixed(1) : "0"
    }));
  }, [data]);

  return (
    <ResponsiveContainer width="100%" height="100%">
      <div className="h-full flex flex-col">
        <h3 className="text-center text-lg font-semibold mb-4">Session Duration Distribution</h3>
        <div className="flex-1">
          <BarChart data={durationData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="range" />
            <YAxis />
            <Tooltip formatter={(value: any) => [`${value} sessions`, 'Count']} />
            <Bar dataKey="count" fill="#3b82f6" radius={[4, 4, 0, 0]} />
          </BarChart>
        </div>
      </div>
    </ResponsiveContainer>
  );
};

// Peak Hours Heatmap Chart
const PeakHoursChart: React.FC<{ data: DepartmentStats[] }> = ({ data }) => {
  const hourlyData = useMemo(() => {
    const hourCounts = Array.from({ length: 24 }, (_, i) => ({ hour: i, count: 0, usage: 0 }));

    data.forEach(stat => {
      const hour = new Date(stat.acctstarttime).getHours();
      const usage = (Number(stat.acctinputoctets) + Number(stat.acctoutputoctets)) / (1024 * 1024); // MB
      hourCounts[hour].count++;
      hourCounts[hour].usage += usage;
    });

    return hourCounts.map(item => ({
      ...item,
      hourLabel: `${item.hour.toString().padStart(2, '0')}:00`,
      usage: parseFloat(item.usage.toFixed(2))
    }));
  }, [data]);

  return (
    <ResponsiveContainer width="100%" height="100%">
      <div className="h-full flex flex-col">
        <h3 className="text-center text-lg font-semibold mb-4">Peak Usage Hours</h3>
        <div className="flex-1">
          <LineChart data={hourlyData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="hourLabel" interval={2} />
            <YAxis yAxisId="left" />
            <YAxis yAxisId="right" orientation="right" />
            <Tooltip />
            <Legend />
            <Bar yAxisId="left" dataKey="count" fill="#10b981" name="Sessions" />
            <Line yAxisId="right" type="monotone" dataKey="usage" stroke="#f59e0b" strokeWidth={2} name="Usage (MB)" />
          </LineChart>
        </div>
      </div>
    </ResponsiveContainer>
  );
};

// Connection Quality Analysis Chart
const ConnectionQualityChart: React.FC<{ data: DepartmentStats[] }> = ({ data }) => {
  const qualityData = useMemo(() => {
    const terminationCauses = data.reduce((acc: any, stat) => {
      const cause = stat.acctterminatecause || "Unknown";
      acc[cause] = (acc[cause] || 0) + 1;
      return acc;
    }, {});

    return Object.entries(terminationCauses)
      .map(([cause, count]) => ({
        cause: cause === "" ? "Active" : cause,
        count: count as number,
        percentage: ((count as number / data.length) * 100).toFixed(1)
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 6);
  }, [data]);

  return (
    <ResponsiveContainer width="100%" height="100%">
      <div className="h-full flex flex-col">
        <h3 className="text-center text-lg font-semibold mb-4">Connection Quality Analysis</h3>
        <div className="flex-1">
          <PieChart>
            <Pie
              data={qualityData}
              dataKey="count"
              nameKey="cause"
              outerRadius={100}
              label={({ cause, percentage }) => `${cause} (${percentage}%)`}
            >
              {qualityData.map((_, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <Tooltip formatter={(value: any) => [`${value} sessions`, 'Count']} />
            <Legend />
          </PieChart>
        </div>
      </div>
    </ResponsiveContainer>
  );
};

// Staff Users Pie Chart - Top 5 Staff Users
const StaffPieChart: React.FC<{ data: DepartmentStats[]; customers: any[] }> = ({ data, customers }) => {
  // Check if data is available
  if (!data || data.length === 0) {
    return (
      <ResponsiveContainer width="100%" height="100%">
        <div className="flex items-center justify-center h-full">
          <div className="text-center text-gray-500">
            <h3 className="text-lg font-semibold mb-2">Top 5 Staff Users by Usage (GB)</h3>
            <p>No analytics data available</p>
          </div>
        </div>
      </ResponsiveContainer>
    );
  }

  // Get all customer usernames to identify staff (empty set if no customers loaded yet)
  const customerUsernames = new Set(customers && customers.length > 0 ? customers.map((customer: any) => customer.username) : []);

  // Filter for staff users only
  const staffData = data.filter((stat: DepartmentStats) => customerUsernames.has(stat.username));

  // If no staff data found, show appropriate message
  if (staffData.length === 0) {
    const message = customers && customers.length > 0 ? "No staff usage found" : "Loading staff data...";
    return (
      <ResponsiveContainer width="100%" height="100%">
        <div className="flex items-center justify-center h-full">
          <div className="text-center text-gray-500">
            <h3 className="text-lg font-semibold mb-2">Top 5 Staff Users by Usage (GB)</h3>
            <p>{message}</p>
          </div>
        </div>
      </ResponsiveContainer>
    );
  }

  // Aggregate staff usage by username
  const staffUsageMap = new Map<string, number>();
  staffData.forEach((stat: DepartmentStats) => {
    const username = stat.username;
    const usage = (Number(stat.acctinputoctets || 0) + Number(stat.acctoutputoctets || 0)) / (1024 * 1024 * 1024);
    staffUsageMap.set(username, (staffUsageMap.get(username) || 0) + usage);
  });

  const staffUsageArray = Array.from(staffUsageMap.entries())
    .map(([name, total]) => ({ name, total: parseFloat(total.toFixed(2)) }))
    .filter((item) => item.total > 0)
    .sort((a, b) => b.total - a.total);

  const top5Staff = staffUsageArray.slice(0, 5);
  const othersTotal = staffUsageArray.slice(5).reduce((sum, item) => sum + item.total, 0);

  if (othersTotal > 0) {
    top5Staff.push({ name: "Others", total: parseFloat(othersTotal.toFixed(2)) });
  }

  const chartData = top5Staff.length ? top5Staff : [{ name: "No staff data", total: 0 }];

  return (
    <ResponsiveContainer width="100%" height="100%">
      <section><header className="text-center">Top 5 Staff Users by Usage (GB)</header></section>
      <PieChart>
        <Pie
          data={chartData}
          dataKey="total"
          nameKey="name"
          outerRadius={100}
          label={({ name, percent }) => `${name} (${(percent * 100).toFixed(1)}%)`}
        >
          {chartData.map((_entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
        </Pie>
        <Tooltip formatter={(value: any) => [`${value} GB`, 'Usage']} />
        <Legend verticalAlign="top" align="center" layout="horizontal" wrapperStyle={{ top: 0, right: 0 }} />
      </PieChart>
    </ResponsiveContainer>
  );
};

// Guest Users Pie Chart - Top 5 Guest Users
const GuestPieChart: React.FC<{ data: DepartmentStats[]; customers: any[] }> = ({ data, customers }) => {
  // Check if data is available
  if (!data || data.length === 0) {
    return (
      <ResponsiveContainer width="100%" height="100%">
        <div className="flex items-center justify-center h-full">
          <div className="text-center text-gray-500">
            <h3 className="text-lg font-semibold mb-2">Top 5 Guest Users by Usage (GB)</h3>
            <p>No guest data available</p>
          </div>
        </div>
      </ResponsiveContainer>
    );
  }

  // Get all customer usernames to identify staff (empty set if no customers)
  const customerUsernames = new Set(customers && customers.length > 0 ? customers.map((customer: any) => customer.username) : []);

  // Filter for guest users only (users not in customer list)
  const guestData = data.filter((stat: DepartmentStats) => !customerUsernames.has(stat.username));

  // If no guest data found, show appropriate message
  if (guestData.length === 0) {
    return (
      <ResponsiveContainer width="100%" height="100%">
        <div className="flex items-center justify-center h-full">
          <div className="text-center text-gray-500">
            <h3 className="text-lg font-semibold mb-2">Top 5 Guest Users by Usage (GB)</h3>
            <p>No guest usage found</p>
          </div>
        </div>
      </ResponsiveContainer>
    );
  }

  // Aggregate guest usage by username
  const guestUsageMap = new Map<string, number>();
  guestData.forEach((stat: DepartmentStats) => {
    const username = stat.username;
    const usage = (Number(stat.acctinputoctets || 0) + Number(stat.acctoutputoctets || 0)) / (1024 * 1024 * 1024);
    guestUsageMap.set(username, (guestUsageMap.get(username) || 0) + usage);
  });

  const guestUsageArray = Array.from(guestUsageMap.entries())
    .map(([name, total]) => ({ name, total: parseFloat(total.toFixed(2)) }))
    .filter((item) => item.total > 0)
    .sort((a, b) => b.total - a.total);

  const top5Guests = guestUsageArray.slice(0, 5);
  const othersTotal = guestUsageArray.slice(5).reduce((sum, item) => sum + item.total, 0);

  if (othersTotal > 0) {
    top5Guests.push({ name: "Others", total: parseFloat(othersTotal.toFixed(2)) });
  }

  const chartData = top5Guests.length ? top5Guests : [{ name: "No guest data", total: 0 }];

  return (
    <ResponsiveContainer width="100%" height="100%">
      <section><header className="text-center">Top 5 Guest Users by Usage (GB)</header></section>
      <PieChart>
        <Pie
          data={chartData}
          dataKey="total"
          nameKey="name"
          outerRadius={100}
          label={({ name, percent }) => `${name} (${(percent * 100).toFixed(1)}%)`}
        >
          {chartData.map((_entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
        </Pie>
        <Tooltip formatter={(value: any) => [`${value} GB`, 'Usage']} />
        <Legend verticalAlign="top" align="center" layout="horizontal" wrapperStyle={{ top: 0, right: 0 }} />
      </PieChart>
    </ResponsiveContainer>
  );
};


const AnalyticsLineChart: React.FC<{ data: LineChartData[], startDate: string }> = ({ data, startDate }) => {
  // Data is already filled with missing days from the processing function
  const chartData = data?.length ? data : [
    { timestamp: startDate, totalUsage: 0, count: 0 }
  ];

  return (
    <ResponsiveContainer width="100%" height="100%">
      <h1 className="text-center">Data Usage Over Time (MB)</h1>
      <LineChart data={chartData} margin={{ top: 1, right: 5, left: 5, bottom: 0 }}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis
          dataKey="timestamp"
          type="category"
          interval="preserveStartEnd"
          tickFormatter={(v: any) => {
            try {
              const date = new Date(v);
              if (isNaN(date.getTime())) return "";

              // Check if this is hourly data (has time component) or daily data
              const isHourlyData = v.includes('T') && !v.endsWith('T00:00:00.000Z');

              if (isHourlyData) {
                // For hourly data, show date and hour
                return date.toLocaleDateString(undefined, {
                  month: "short",
                  day: "numeric",
                  // }) + " " + date.toLocaleTimeString(undefined, {
                  //   hour: "2-digit",
                  //   hour12: false
                });
              } else {
                // For daily data, show just the date
                return date.toLocaleDateString(undefined, {
                  day: "2-digit",
                  month: "short",
                });
              }
            } catch {
              return "";
            }
          }}
          angle={-45}
          textAnchor="end"
          height={80}
        />
        <YAxis
          domain={[0, "auto"]}
          allowDecimals={false}
        />
        <Tooltip />
        <Legend verticalAlign="top" align="right" layout="horizontal" wrapperStyle={{ top: 0, right: 0 }} />
        <Line
          type="monotone"
          dataKey="totalUsage"
          stroke="#e44d12ff"
          strokeWidth={2}
          dot={false}
          activeDot={{ r: 4 }}
        />
      </LineChart>
    </ResponsiveContainer>
  );
};

const AnalyticsLineChartforCount: React.FC<{ data: LineChartData[]; startDate: string; endDate: string }> = ({ data, startDate, endDate }) => {
  // Data is already filled with missing hours/days from the processing function
  const chartData = data?.length ? data : [
    { timestamp: startDate, totalUsage: 0, count: 0 }
  ];

  // Fill missing dates by aggregating hourly data to daily data
  const filledChartData = fillMissingDates(chartData, startDate, endDate);

  return (
    <ResponsiveContainer width="100%" height="100%">
      <h1 className="text-center">Active Users Count Over Time</h1>
      <LineChart data={filledChartData} margin={{ top: 1, right: 5, left: 5, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis
          dataKey="timestamp"
          type="category"
          interval="preserveStartEnd"
          tickFormatter={(v: any) => {
            try {
              const date = new Date(v);
              if (isNaN(date.getTime())) return "";
              return date.toLocaleDateString(undefined, {
                day: "2-digit",
                month: "short",   // e.g., "Jul"
              });
            } catch {
              return "";
            }
          }}
          angle={-45}
          textAnchor="end"
          height={60}
        />

        <YAxis
          domain={[0, "auto"]} // Always starts at 0
          allowDecimals={false}
        />

        <Legend verticalAlign="top" align="right" layout="horizontal" wrapperStyle={{ top: 0, right: 0 }} />
        <Line
          type="step"
          dataKey="count"
          stroke="#3b82f6"
          strokeWidth={2}
          dot={false}
          activeDot={{ r: 4 }}
        />
      </LineChart>
    </ResponsiveContainer>
  );
};

// Department Overview Chart with Staff/Guest separation
const DepartmentOverviewChart: React.FC<{ data: DepartmentOverviewData[] }> = ({ data }) => {
  const chartData = data.length ? data : [{
    departmentName: "No data",
    staffUsers: 0,
    guestUsers: 0,
    staffUsage: 0,
    guestUsage: 0,
    totalUsage: 0,
    totalUsers: 0
  }];

  return (
    <ResponsiveContainer width="100%" height="100%">
      <section><header className="text-center">Department Usage: Staff vs Guest (GB)</header></section>
      <BarChart
        data={chartData}
        margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
      >
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis
          dataKey="departmentName"
          // angle={-45}
          // textAnchor="end"
          height={80}
        />
        <YAxis />
        <Tooltip
          formatter={(value: any, name: string) => [
            `${parseFloat(value).toFixed(2)} GB`,
            name === 'staffUsage' ? 'Staff Usage' : 'Guest Usage'
          ]}
          labelFormatter={(label: any) => `Department: ${label}`}
        />
        <Legend verticalAlign="top" align="right" layout="horizontal" wrapperStyle={{ top: 0, right: 0 }} />
        <Bar
          dataKey="staffUsage"
          fill="#22c55e"
          name="Staff Usage"
        />
        <Bar
          dataKey="guestUsage"
          fill="#f59e0b"
          name="Guest Usage"
        />
      </BarChart>
    </ResponsiveContainer>
  );
};

// Department Overview Summary Table
const DepartmentOverviewTable: React.FC<{ data: DepartmentOverviewData[]; loading?: boolean }> = ({ data, loading = false }) => {
  console.log('DepartmentOverviewTable received data:', data, 'loading:', loading);

  if (loading) {
    return (
      <div className="text-center py-8 text-gray-500">
        <h3 className="text-lg font-semibold mb-4">Department Overview: Staff vs Guest</h3>
        <p>Loading department data...</p>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <h3 className="text-lg font-semibold mb-4">Department Overview: Staff vs Guest</h3>
        <p>No department data available. No usage found for the selected period.</p>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <h3 className="text-lg font-semibold mb-4 text-center">Department Overview: Staff vs Guest</h3>
      <table className="min-w-full bg-white border border-gray-200">
        <thead className="bg-gray-100">
          <tr>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              Department
            </th>
            <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              Staff Users
            </th>
            <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              Guest Users
            </th>
            <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              Staff Usage (GB)
            </th>
            <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              Guest Usage (GB)
            </th>
            <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              Total Usage (GB)
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200">
          {data.map((dept: DepartmentOverviewData, index: number) => (
            <tr key={dept.departmentName} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
              <td className="px-4 py-2 text-sm font-medium text-gray-900">
                {dept.departmentName}
              </td>
              <td className="px-4 py-2 text-sm text-center text-gray-700">
                {dept.staffUsers}
              </td>
              <td className="px-4 py-2 text-sm text-center text-gray-700">
                {dept.guestUsers}
              </td>
              <td className="px-4 py-2 text-sm text-center text-green-600 font-medium">
                {dept.staffUsage}
              </td>
              <td className="px-4 py-2 text-sm text-center text-yellow-600 font-medium">
                {dept.guestUsage}
              </td>
              <td className="px-4 py-2 text-sm text-center text-blue-600 font-bold">
                {dept.totalUsage}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default function AnalyticsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const defaultDates = getDefaultDates();

  // Simplified state management
  const [departments, setDepartments] = useState<PackageInfo[]>([]);
  const [selectedDepartment, setSelectedDepartment] = useState<string>("");
  const [aggregatedStats, setAggregatedStats] = useState<DepartmentStats[]>([]);
  const [individualStats, setIndividualStats] = useState<DepartmentStats[]>([]);
  const [activeStats, setActiveStats] = useState<ActiveStat[]>([]);
  const [customers, setCustomers] = useState<any[]>([]);
  const [departmentOverviewData, setDepartmentOverviewData] = useState<DepartmentOverviewData[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [startDate, setStartDate] = useState(defaultDates.start);
  const [endDate, setEndDate] = useState(defaultDates.end);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState<string>("15");
  const [lineChartData, setLineChartData] = useState<LineChartData[]>([]);
  const [lineChartDataforCount, setLineChartDataforCount] = useState<LineChartData[]>([]);

  // Modal state
  const [selectedUser, setSelectedUser] = useState<string | null>(null);
  const [userSessions, setUserSessions] = useState<DepartmentStats[]>([]);
  const [modalCurrentPage, setModalCurrentPage] = useState(1);
  const [modalItemsPerPage, setModalItemsPerPage] = useState(10);
  const [modalSearch, setModalSearch] = useState("");

  // Get active view from URL params
  const getActiveView = (): "aggregated" | "individual" | "active" => {
    const typeParam = searchParams.get("type");
    if (typeParam === "individualview") return "individual";
    if (typeParam === "activesessions") return "active";
    return "aggregated";
  };

  // Get active tab from URL params
  const getActiveTab = (): "overview" | "users" | "network" | "department" => {
    const tabParam = searchParams.get("tab");
    if (tabParam === "users") return "users";
    if (tabParam === "network") return "network";
    if (tabParam === "department") return "department";
    return "overview";
  };

  const [activeView, setActiveView] = useState<"aggregated" | "individual" | "active">(getActiveView);
  const [activeTab, setActiveTab] = useState<"overview" | "users" | "network" | "department">(getActiveTab);

  // Simplified event handlers
  const handleDepartmentChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedDepartment(e.target.value);
  };

  const handleItemsPerPageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setItemsPerPage(e.target.value);
    setCurrentPage(1);
  };

  const handleViewChange = (view: "aggregated" | "individual" | "active") => {
    setActiveView(view);
    setCurrentPage(1);

    const params = new URLSearchParams(searchParams.toString());
    const viewTypes = { individual: "individualview", active: "activesessions", aggregated: "aggregatedview" };
    params.set("type", viewTypes[view]);
    router.replace(`?${params.toString()}`);
  };

  const handleTabChange = (tab: "overview" | "users" | "network" | "department") => {
    setActiveTab(tab);
    setCurrentPage(1);

    const params = new URLSearchParams(searchParams.toString());
    params.set("tab", tab);
    router.replace(`?${params.toString()}`);
  };

  const handleUsernameClick = (username: string) => {
    const sessions = individualStats.filter((session: DepartmentStats) => session.username === username);
    setUserSessions(sessions);
    setSelectedUser(username);
    setModalCurrentPage(1);
  };

  const closeUserSessionsModal = () => {
    setSelectedUser(null);
    setModalCurrentPage(1);
    setModalSearch("");
  };

  // Simplified data processing functions
  const aggregateUserData = (rawData: DepartmentStats[]): DepartmentStats[] => {
    const userMap = new Map<string, DepartmentStats>();

    rawData.forEach((curr: DepartmentStats) => {
      const existing = userMap.get(curr.username);
      if (existing) {
        existing.acctinputoctets = (Number(existing.acctinputoctets) + Number(curr.acctinputoctets)).toString();
        existing.acctoutputoctets = (Number(existing.acctoutputoctets) + Number(curr.acctoutputoctets)).toString();
      } else {
        userMap.set(curr.username, { ...curr });
      }
    });

    return Array.from(userMap.values());
  };

  const processLineChartData = (rawData: DepartmentStats[], startDate: string, endDate: string): LineChartData[] => {
    const dailyUsageMap = new Map<string, number>();

    rawData.forEach((item: DepartmentStats) => {
      const startTime = new Date(item.acctstarttime);
      // Get just the date part (YYYY-MM-DD) for daily aggregation
      const dateKey = startTime.toISOString().split('T')[0];

      const totalUsageMB = (Number(item.acctinputoctets) + Number(item.acctoutputoctets)) / 1024 / 1024;
      dailyUsageMap.set(dateKey, (dailyUsageMap.get(dateKey) || 0) + totalUsageMB);
    });

    const chartData = Array.from(dailyUsageMap.entries())
      .map(([timestamp, totalUsage]: [string, number]) => ({
        timestamp,
        totalUsage: parseFloat(totalUsage.toFixed(2)),
        count: 0
      }))
      .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

    // Fill missing dates with zero values
    return fillMissingDates(chartData, startDate, endDate);
  };

  const processLineChartDataForCount = (rawData: DepartmentStats[], startDate: string, endDate: string): LineChartData[] => {
    const hourlyCountMap = new Map<string, Set<string>>();

    rawData.forEach((item: DepartmentStats) => {
      const startTime = new Date(item.acctstarttime);
      startTime.setMinutes(0, 0, 0);
      const timestampKey = startTime.toISOString();

      if (!hourlyCountMap.has(timestampKey)) {
        hourlyCountMap.set(timestampKey, new Set<string>());
      }
      // Count unique users per hour
      hourlyCountMap.get(timestampKey)!.add(item.username);
    });

    const chartData = Array.from(hourlyCountMap.entries())
      .map(([timestamp, userSet]: [string, Set<string>]) => ({
        timestamp,
        totalUsage: 0,
        count: userSet.size
      }))
      .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

    // Fill missing hours with zero values
    return fillMissingHours(chartData, startDate, endDate);
  };

  const fetchDepartmentStats = async () => {
    setLoading(true);
    const payload = {
      group: selectedDepartment ? [selectedDepartment] : departments.map((d: PackageInfo) => d.package_name),
      // start: `${startDate} 00:00:00`,
      // end: `${endDate} 00:00:00`,
      start: "2025-07-01 00:00:00",
      end: "2025-07-21 00:00:00"
    };

    try {
      const res = await apiClient.post("/department/stats", payload);
      const rawData = res?.data || [];

      setIndividualStats(rawData);
      setAggregatedStats(aggregateUserData(rawData));
      setLineChartData(processLineChartData(rawData, startDate, endDate));
      setLineChartDataforCount(processLineChartDataForCount(rawData, startDate, endDate));

      // Process department overview data with staff/guest separation
      // Always process the data, even if customers is empty (all will be treated as guests)
      const overviewData = processDepartmentOverviewData(rawData, departments, customers);
      console.log('Setting department overview data:', overviewData);
      setDepartmentOverviewData(overviewData);
    } catch (err) {
      console.error("Error fetching department stats:", err);
      toast.error("Error fetching department stats");
    } finally {
      setLoading(false);
    }
  };

  const fetchActiveSessionsData = async () => {
    setLoading(true);
    try {
      const payload = {
        group: departments.map((d: PackageInfo) => d.package_name),
        limit: 1,
      };

      const res = await apiClient.post("/analytics", payload);
      const analyticsData = res?.data || [];
      console.log("Active sessions data:", analyticsData);

      if (analyticsData.length > 0) {
        const latestRecord = analyticsData[0];
        const activeUsers = latestRecord.data.map((user: any) => ({
          username: user.username,
          acctstarttime: user.acctstarttime,
          acctinputoctets: user.acctinputoctets,
          acctoutputoctets: user.acctoutputoctets,
          inputMbps: latestRecord.inputMbps || 0,
          outputMbps: latestRecord.outputMbps || 0,
          count: latestRecord.count,
          recordId: latestRecord.id,
          created_at: latestRecord.created_at,
        }));
        setActiveStats(activeUsers);
      } else {
        setActiveStats([]);
      }
    } catch (err) {
      console.error("Error fetching active sessions:", err);
      toast.error("Error fetching active sessions");
      setActiveStats([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchDepartments = async () => {
    try {
      const res = await apiClient.get("/package");
      setDepartments(res?.data || []);
    } catch (err) {
      console.error("Failed to fetch departments:", err);
    }
  };

  const fetchCustomers = async () => {
    try {
      const res = await apiClient.get("/customer");
      const customerData = res?.data || [];
      console.log('Fetched customers:', customerData);
      setCustomers(customerData);
    } catch (err) {
      console.error("Failed to fetch customers:", err);
      // Set empty array on error so the component still works
      setCustomers([]);
    }
  };

  // Process department overview data with staff/guest separation
  const processDepartmentOverviewData = (
    rawData: DepartmentStats[],
    departments: PackageInfo[],
    customers: any[]
  ): DepartmentOverviewData[] => {
    console.log('Processing department overview data:', {
      rawDataLength: rawData.length,
      departmentsLength: departments.length,
      customersLength: customers.length
    });

    if (!rawData || rawData.length === 0) {
      console.log('No raw data available');
      return [];
    }

    if (!departments || departments.length === 0) {
      console.log('No departments available');
      return [];
    }

    const result = departments.map((dept) => {
      // Get all stats for this department
      const deptStats = rawData.filter((stat) => stat.groupname === dept.package_name);

      if (deptStats.length === 0) {
        console.log(`No stats found for department ${dept.package_name}`);
        return null;
      }

      // Get customers for this department (if customers data is available)
      const deptCustomers = customers.length > 0 ? customers.filter((customer) => customer.pack_id === dept.id) : [];
      const deptCustomerUsernames = new Set(deptCustomers.map((customer) => customer.username));

      console.log(`Department ${dept.package_name}:`, {
        deptStatsLength: deptStats.length,
        deptCustomersLength: deptCustomers.length,
        customerUsernames: Array.from(deptCustomerUsernames)
      });

      // Separate staff and guest data
      const staffStats = deptStats.filter((stat) => deptCustomerUsernames.has(stat.username));
      const guestStats = deptStats.filter((stat) => !deptCustomerUsernames.has(stat.username));

      // Calculate usage in GB
      const staffUsage = staffStats.reduce((sum, stat) =>
        sum + (Number(stat.acctinputoctets || 0) + Number(stat.acctoutputoctets || 0)), 0
      ) / (1024 * 1024 * 1024);

      const guestUsage = guestStats.reduce((sum, stat) =>
        sum + (Number(stat.acctinputoctets || 0) + Number(stat.acctoutputoctets || 0)), 0
      ) / (1024 * 1024 * 1024);

      // Get unique users
      const staffUsers = new Set(staffStats.map((stat) => stat.username)).size;
      const guestUsers = new Set(guestStats.map((stat) => stat.username)).size;

      const deptResult = {
        departmentName: dept.package_name,
        staffUsers,
        guestUsers,
        staffUsage: parseFloat(staffUsage.toFixed(2)),
        guestUsage: parseFloat(guestUsage.toFixed(2)),
        totalUsage: parseFloat((staffUsage + guestUsage).toFixed(2)),
        totalUsers: staffUsers + guestUsers,
      };

      console.log(`Department ${dept.package_name} result:`, deptResult);
      return deptResult;
    }).filter((dept) => dept !== null && (dept.totalUsage > 0 || dept.totalUsers > 0)) as DepartmentOverviewData[]; // Include departments with usage or users

    console.log('Final processed data:', result);
    return result;
  };

  // Effects
  useEffect(() => {
    fetchDepartments();
    fetchCustomers();
  }, []);

  useEffect(() => {
    const newView = getActiveView();
    const newTab = getActiveTab();
    if (newView !== activeView) {
      setActiveView(newView);
      setCurrentPage(1);
    }
    if (newTab !== activeTab) {
      setActiveTab(newTab);
      setCurrentPage(1);
    }
  }, [searchParams, activeView, activeTab]);

  useEffect(() => {
    if (departments.length > 0) {
      if (activeView === "active") {
        fetchActiveSessionsData();
      } else {
        fetchDepartmentStats();
      }
    }
  }, [departments, customers, activeView, selectedDepartment, startDate, endDate]);

  // Reprocess department overview data when customers are loaded
  useEffect(() => {
    if (customers.length > 0 && individualStats.length > 0 && departments.length > 0) {
      setDepartmentOverviewData(processDepartmentOverviewData(individualStats, departments, customers));
    }
  }, [customers, individualStats, departments]);

  // Handle Escape key to close modal
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape" && selectedUser) {
        closeUserSessionsModal();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [selectedUser]);

  // Get current data and pagination
  const currentData = activeView === "aggregated" ? aggregatedStats :
    activeView === "individual" ? individualStats : activeStats;

  const itemsPerPageNum = itemsPerPage === "all" ? currentData.length : parseInt(itemsPerPage, 10);
  const totalPages = itemsPerPageNum === currentData.length ? 1 : Math.ceil(currentData.length / itemsPerPageNum);
  const currentStats = currentData.slice((currentPage - 1) * itemsPerPageNum, currentPage * itemsPerPageNum);

  // Bar chart data formatting with sorting for top 10 users
  const formattedBarChartData: BarChartData[] = useMemo(() => {
    return aggregatedStats
      .map((stat: DepartmentStats) => ({
        name: stat.username,
        download: parseFloat((Number(stat.acctoutputoctets) / (1024 * 1024 * 1024)).toFixed(2)),
        upload: parseFloat((Number(stat.acctinputoctets) / (1024 * 1024 * 1024)).toFixed(2)),
      }))
      .sort((a: BarChartData, b: BarChartData) => (b.download + b.upload) - (a.download + a.upload)) // Sort by total usage descending
      .slice(0, 10); // Take only top 10 users
  }, [aggregatedStats]);

  // Department bar chart data formatting
  const formattedDepartmentBarChartData: BarChartData[] = useMemo(() => {
    return departments
      .map((dept: PackageInfo) => {
        const deptStats = aggregatedStats.filter(
          (stat: DepartmentStats) => stat.groupname === dept.package_name
        );

        const totalDownload = deptStats.reduce(
          (sum: number, stat: DepartmentStats) => sum + Number(stat.acctoutputoctets),
          0
        ) / (1024 * 1024 * 1024); // Convert to GB

        const totalUpload = deptStats.reduce(
          (sum: number, stat: DepartmentStats) => sum + Number(stat.acctinputoctets),
          0
        ) / (1024 * 1024 * 1024); // Convert to GB

        return {
          name: dept.package_name,
          download: parseFloat(totalDownload.toFixed(2)),
          upload: parseFloat(totalUpload.toFixed(2)),
        };
      })
      .filter((dept: BarChartData) => dept.download > 0 || dept.upload > 0) // Only include departments with usage
      .sort((a: BarChartData, b: BarChartData) => (b.download + b.upload) - (a.download + a.upload)) // Sort by total usage descending
      .slice(0, 10); // Take only top 10 departments
  }, [departments, aggregatedStats]);

  const sortedDepartments = [...departments]
    .map((dept: PackageInfo) => {
      const deptStats = aggregatedStats.filter(
        (stat: DepartmentStats) => stat.groupname === dept.package_name
      );
      const totalUsage =
        deptStats.reduce(
          (sum: number, stat: DepartmentStats) =>
            sum +
            (Number(stat.acctinputoctets) + Number(stat.acctoutputoctets)),
          0
        ) /
        (1024 * 1024 * 1024); // GB

      return {
        ...dept,
        totalUsage,
        userCount: deptStats.length,
      };
    })
    .sort((a, b) => b.totalUsage - a.totalUsage);

  return (
    <div className="p-5 sm:p-5 w-full">

      {/* Constant Heading */}
      <div className="mb-6">
        <h1 className="text-xl sm:text-xl font-bold text-wrap">
          Analytics Dashboard
        </h1>
      </div>

      {/* Main Navigation Tabs with Date Selector */}
      <div className="mb-6">
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
          {/* Navigation Tabs */}
          <div className="flex flex-wrap gap-2">
            {['overview', 'users', 'network', 'department'].map((tab) => (
              <button
                key={tab}
                onClick={() => handleTabChange(tab as "overview" | "users" | "network" | "department")}
                className={`px-4 py-2 rounded-full border w-full sm:w-auto h-10 capitalize ${activeTab === tab
                  ? "bg-blue-600 text-white"
                  : "border-gray-300 text-gray-700 bg-white"
                  }`}
              >
                {tab}
              </button>
            ))}
          </div>

          {/* Date Selector - Only show for non-active views */}
          {activeView !== "active" && (
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
              <span className="text-sm font-medium text-gray-700">
                Date Range:
              </span>
              <div className="flex flex-col sm:flex-row items-center gap-2">
                <input
                  type="date"
                  value={startDate}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setStartDate(e.target.value)
                  }
                  className="border border-gray-300 rounded-md px-3 py-2 text-sm bg-white shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                />
                <span className="text-gray-400 text-sm">to</span>
                <input
                  type="date"
                  value={endDate}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setEndDate(e.target.value)
                  }
                  className="border border-gray-300 rounded-md px-3 py-2 text-sm bg-white shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Enhanced Stats Cards - Only show on overview page */}
      {activeTab === 'overview' && (
        <div className="mb-6 space-y-4">
          {/* Primary Stats Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Total Sessions Card */}
            <div className="p-4 bg-gradient-to-br from-blue-600 to-blue-800 rounded-xl border border-blue-300 cursor-pointer hover:from-blue-700 hover:to-blue-900 transition-all duration-300 shadow-lg"
              onClick={() => router.push('/app/analytics?type=aggregatedview&tab=users')}>
              <div className="flex items-center justify-between w-full">
                <div className="flex flex-col">
                  <h1 className="text-white font-medium text-sm mb-2">Total Sessions</h1>
                  <strong className="text-white text-3xl font-bold mb-2">{individualStats.length}</strong>
                  <div className="flex flex-col text-xs text-blue-200">
                    <span>Staff: {(() => {
                      const customerUsernames = new Set(customers.map((customer: any) => customer.username));
                      return individualStats.filter((stat: DepartmentStats) => customerUsernames.has(stat.username)).length;
                    })()}</span>
                    <span>Guest: {(() => {
                      const customerUsernames = new Set(customers.map((customer: any) => customer.username));
                      return individualStats.filter((stat: DepartmentStats) => !customerUsernames.has(stat.username)).length;
                    })()}</span>
                  </div>
                </div>
                <Sessions className="text-white w-8 h-8 opacity-80" />
              </div>
            </div>

            {/* Online Users Card */}
            <div className="p-4 bg-gradient-to-br from-green-600 to-green-800 rounded-xl border border-green-300 cursor-pointer hover:from-green-700 hover:to-green-900 transition-all duration-300 shadow-lg"
              onClick={() => router.push('/app/analytics?type=activesessions&tab=users')}>
              <div className="flex items-center justify-between w-full">
                <div className="flex flex-col">
                  <h1 className="text-white font-medium text-sm mb-2">Online Users</h1>
                  <strong className="text-white text-3xl font-bold mb-2">{activeStats.length > 0 ? activeStats[0]?.count || 0 : 0}</strong>
                  <div className="flex flex-col text-xs text-green-200">
                    <span>Staff: {(() => {
                      if (activeStats.length === 0) return 0;
                      const customerUsernames = new Set(customers.map((customer: any) => customer.username));
                      return activeStats.filter((stat: ActiveStat) => customerUsernames.has(stat.username)).length;
                    })()}</span>
                    <span>Guest: {(() => {
                      if (activeStats.length === 0) return 0;
                      const customerUsernames = new Set(customers.map((customer: any) => customer.username));
                      return activeStats.filter((stat: ActiveStat) => !customerUsernames.has(stat.username)).length;
                    })()}</span>
                  </div>
                </div>
                <Users className="text-white w-8 h-8 opacity-80" />
              </div>
            </div>

            {/* Total Network Usage Card */}
            <div className="p-4 bg-gradient-to-br from-purple-600 to-purple-800 rounded-xl border border-purple-300 cursor-pointer hover:from-purple-700 hover:to-purple-900 transition-all duration-300 shadow-lg"
              onClick={() => router.push('/app/analytics?type=aggregatedview&tab=network')}>
              <div className="flex items-center justify-between w-full">
                <div className="flex flex-col">
                  <h1 className="text-white font-medium text-sm mb-2">Total Usage</h1>
                  <div className="flex items-center gap-1 mb-2">
                    <strong className="text-white text-3xl font-bold">{(aggregatedStats.reduce((total: number, stat: DepartmentStats) =>
                      total + (Number(stat.acctinputoctets) + Number(stat.acctoutputoctets)), 0) / (1024 * 1024 * 1024)).toFixed(1)}</strong>
                    <span className="text-purple-200 text-sm">GB</span>
                  </div>
                  <div className="flex flex-col text-xs text-purple-200">
                    <span>↓ {(aggregatedStats.reduce((total: number, stat: DepartmentStats) =>
                      total + Number(stat.acctoutputoctets), 0) / (1024 * 1024 * 1024)).toFixed(1)} GB</span>
                    <span>↑ {(aggregatedStats.reduce((total: number, stat: DepartmentStats) =>
                      total + Number(stat.acctinputoctets), 0) / (1024 * 1024 * 1024)).toFixed(1)} GB</span>
                  </div>
                </div>
                <Network className="text-white w-8 h-8 opacity-80" />
              </div>
            </div>

            {/* Average Session Duration Card */}
            <div className="p-4 bg-gradient-to-br from-orange-600 to-orange-800 rounded-xl border border-orange-300 cursor-pointer hover:from-orange-700 hover:to-orange-900 transition-all duration-300 shadow-lg">
              <div className="flex items-center justify-between w-full">
                <div className="flex flex-col">
                  <h1 className="text-white font-medium text-sm mb-2">Avg Session</h1>
                  <strong className="text-white text-3xl font-bold mb-2">{(() => {
                    const completedSessions = individualStats.filter((stat: DepartmentStats) => stat.acctstoptime);
                    if (completedSessions.length === 0) return "0m";
                    const totalDuration = completedSessions.reduce((sum: number, stat: DepartmentStats) => {
                      const start = new Date(stat.acctstarttime).getTime();
                      const stop = new Date(stat.acctstoptime).getTime();
                      return sum + (stop - start);
                    }, 0);
                    const avgMinutes = totalDuration / completedSessions.length / (1000 * 60);
                    return avgMinutes > 60 ? `${(avgMinutes / 60).toFixed(1)}h` : `${avgMinutes.toFixed(0)}m`;
                  })()}</strong>
                  <div className="flex flex-col text-xs text-orange-200">
                    <span>Completed: {individualStats.filter((stat: DepartmentStats) => stat.acctstoptime).length}</span>
                    <span>Active: {individualStats.filter((stat: DepartmentStats) => !stat.acctstoptime).length}</span>
                  </div>
                </div>
                <div className="text-white w-8 h-8 opacity-80 flex items-center justify-center">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* Secondary Stats Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Real-time Bandwidth Card */}
            <div className="p-4 bg-gradient-to-br from-teal-600 to-teal-800 rounded-xl border border-teal-300 shadow-lg">
              <div className="flex items-center justify-between w-full">
                <div className="flex flex-col">
                  <h1 className="text-white font-medium text-sm mb-2">Live Bandwidth</h1>
                  <div className="flex items-center gap-1 mb-2">
                    <strong className="text-white text-2xl font-bold">{activeStats.length > 0 ? (activeStats[0]?.inputMbps + activeStats[0]?.outputMbps).toFixed(1) : "0"}</strong>
                    <span className="text-teal-200 text-sm">Mbps</span>
                  </div>
                  <div className="flex flex-col text-xs text-teal-200">
                    <span>↓ {activeStats.length > 0 ? activeStats[0]?.outputMbps?.toFixed(1) || "0" : "0"} Mbps</span>
                    <span>↑ {activeStats.length > 0 ? activeStats[0]?.inputMbps?.toFixed(1) || "0" : "0"} Mbps</span>
                  </div>
                </div>
                <div className="text-white w-8 h-8 opacity-80 flex items-center justify-center">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
                  </svg>
                </div>
              </div>
            </div>

            {/* Department Performance Card */}
            <div className="p-4 bg-gradient-to-br from-indigo-600 to-indigo-800 rounded-xl border border-indigo-300 shadow-lg">
              <div className="flex items-center justify-between w-full">
                <div className="flex flex-col">
                  <h1 className="text-white font-medium text-sm mb-2">Departments</h1>
                  <strong className="text-white text-2xl font-bold mb-2">{departments.length}</strong>
                  <div className="flex flex-col text-xs text-indigo-200">
                    <span>Active: {departmentOverviewData.filter(d => d.totalUsers > 0).length}</span>
                    <span>Top: {departmentOverviewData.length > 0 ? departmentOverviewData.sort((a, b) => b.totalUsage - a.totalUsage)[0]?.departmentName.substring(0, 8) + "..." : "N/A"}</span>
                  </div>
                </div>
                <div className="text-white w-8 h-8 opacity-80 flex items-center justify-center">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M4 3a2 2 0 100 4h12a2 2 0 100-4H4z" />
                    <path fillRule="evenodd" d="M3 8a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>

            {/* Network Health Card */}
            <div className="p-4 bg-gradient-to-br from-rose-600 to-rose-800 rounded-xl border border-rose-300 shadow-lg">
              <div className="flex items-center justify-between w-full">
                <div className="flex flex-col">
                  <h1 className="text-white font-medium text-sm mb-2">Network Health</h1>
                  <div className="flex items-center gap-1 mb-2">
                    <strong className="text-white text-2xl font-bold">{(() => {
                      const totalTerminated = individualStats.filter((stat: DepartmentStats) => stat.acctterminatecause && stat.acctterminatecause !== "").length;
                      const normalTerminations = individualStats.filter((stat: DepartmentStats) => stat.acctterminatecause === "User-Request" || stat.acctterminatecause === "Session-Timeout").length;
                      return totalTerminated > 0 ? Math.round((normalTerminations / totalTerminated) * 100) : 100;
                    })()}</strong>
                    <span className="text-rose-200 text-sm">%</span>
                  </div>
                  <div className="flex flex-col text-xs text-rose-200">
                    <span>Normal: {individualStats.filter((stat: DepartmentStats) => stat.acctterminatecause === "User-Request" || stat.acctterminatecause === "Session-Timeout").length}</span>
                    <span>Issues: {individualStats.filter((stat: DepartmentStats) => stat.acctterminatecause && stat.acctterminatecause !== "" && stat.acctterminatecause !== "User-Request" && stat.acctterminatecause !== "Session-Timeout").length}</span>
                  </div>
                </div>
                <div className="text-white w-8 h-8 opacity-80 flex items-center justify-center">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}


      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === 'overview' && (
          <>
            {/* Department Overview Summary */}
            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200 mb-6">
              <DepartmentOverviewTable data={departmentOverviewData} loading={loading} />
            </div>

            {/* Charts Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
              <div className="bg-white p-4 rounded-lg h-[400px] flex items-center justify-center border border-green-300">
                <DepartmentOverviewChart data={departmentOverviewData} />
              </div>
              <div className="bg-white p-4 rounded-lg h-[400px] flex items-center justify-center border border-blue-300">
                <AnalyticsLineChart data={lineChartData} startDate={startDate} endDate={endDate} />
              </div>
            </div>

            {/* Advanced Analytics Charts Row 1 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
              <div className="bg-white p-4 rounded-lg h-[400px] flex items-center justify-center border border-purple-300">
                <AnalyticsLineChartforCount data={lineChartDataforCount} startDate={startDate} endDate={endDate} />
              </div>
              <div className="bg-white p-4 rounded-lg h-[400px] flex items-center justify-center border border-yellow-300">
                <AnalyticsBarChart data={formattedBarChartData} />
              </div>
            </div>

            {/* Advanced Analytics Charts Row 2 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
              <div className="bg-white p-4 rounded-lg h-[400px] border border-blue-300">
                <SessionDurationChart data={individualStats} />
              </div>
              <div className="bg-white p-4 rounded-lg h-[400px] border border-teal-300">
                <PeakHoursChart data={individualStats} />
              </div>
            </div>

            {/* Advanced Analytics Charts Row 3 */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
              <div className="bg-white p-4 rounded-lg h-[400px] flex items-center justify-center border border-green-300">
                <StaffPieChart data={individualStats} customers={customers} />
              </div>
              <div className="bg-white p-4 rounded-lg h-[400px] flex items-center justify-center border border-orange-300">
                <GuestPieChart data={individualStats} customers={customers} />
              </div>
              <div className="bg-white p-4 rounded-lg h-[400px] border border-rose-300">
                <ConnectionQualityChart data={individualStats} />
              </div>
            </div>

            {/* Data Insights and Recommendations */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
              {/* Key Insights Card */}
              <div className="bg-gradient-to-br from-slate-50 to-slate-100 p-6 rounded-xl border border-slate-300 shadow-lg">
                <h3 className="text-lg font-semibold mb-4 text-slate-800 flex items-center gap-2">
                  <svg className="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  Key Insights
                </h3>
                <div className="space-y-3">
                  <div className="flex items-start gap-3 p-3 bg-white rounded-lg border border-slate-200">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <p className="text-sm font-medium text-slate-800">Peak Usage Time</p>
                      <p className="text-xs text-slate-600">
                        {(() => {
                          const hourCounts = Array.from({ length: 24 }, () => 0);
                          individualStats.forEach((stat: DepartmentStats) => {
                            const hour = new Date(stat.acctstarttime).getHours();
                            hourCounts[hour]++;
                          });
                          const peakHour = hourCounts.indexOf(Math.max(...hourCounts));
                          return `${peakHour}:00 - ${peakHour + 1}:00 with ${Math.max(...hourCounts)} sessions`;
                        })()}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3 p-3 bg-white rounded-lg border border-slate-200">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <p className="text-sm font-medium text-slate-800">Top Department</p>
                      <p className="text-xs text-slate-600">
                        {departmentOverviewData.length > 0
                          ? `${departmentOverviewData.sort((a, b) => b.totalUsage - a.totalUsage)[0]?.departmentName} (${departmentOverviewData.sort((a, b) => b.totalUsage - a.totalUsage)[0]?.totalUsage.toFixed(1)} GB)`
                          : "No data available"
                        }
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3 p-3 bg-white rounded-lg border border-slate-200">
                    <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <p className="text-sm font-medium text-slate-800">Average Session Quality</p>
                      <p className="text-xs text-slate-600">
                        {(() => {
                          const totalTerminated = individualStats.filter((stat: DepartmentStats) => stat.acctterminatecause && stat.acctterminatecause !== "").length;
                          const normalTerminations = individualStats.filter((stat: DepartmentStats) => stat.acctterminatecause === "User-Request" || stat.acctterminatecause === "Session-Timeout").length;
                          const healthScore = totalTerminated > 0 ? Math.round((normalTerminations / totalTerminated) * 100) : 100;
                          return `${healthScore}% healthy terminations`;
                        })()}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Performance Metrics Card */}
              <div className="bg-gradient-to-br from-slate-50 to-slate-100 p-6 rounded-xl border border-slate-300 shadow-lg">
                <h3 className="text-lg font-semibold mb-4 text-slate-800 flex items-center gap-2">
                  <svg className="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
                  </svg>
                  Performance Metrics
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-4 bg-white rounded-lg border border-slate-200">
                    <div className="text-2xl font-bold text-blue-600">
                      {aggregatedStats.length > 0 ? (
                        (aggregatedStats.reduce((sum: number, stat: DepartmentStats) =>
                          sum + (Number(stat.acctinputoctets) + Number(stat.acctoutputoctets)), 0) / aggregatedStats.length / (1024 * 1024 * 1024)
                        ).toFixed(2)
                      ) : "0"}
                    </div>
                    <div className="text-xs text-slate-600">Avg Usage per User (GB)</div>
                  </div>
                  <div className="p-4 bg-white rounded-lg border border-slate-200">
                    <div className="text-2xl font-bold text-green-600">
                      {activeStats.length > 0 ?
                        ((activeStats[0]?.inputMbps + activeStats[0]?.outputMbps) / (activeStats[0]?.count || 1)).toFixed(1)
                        : "0"
                      }
                    </div>
                    <div className="text-xs text-slate-600">Avg Bandwidth per User (Mbps)</div>
                  </div>
                  <div className="p-4 bg-white rounded-lg border border-slate-200">
                    <div className="text-2xl font-bold text-purple-600">
                      {individualStats.length > 0 ?
                        (individualStats.filter((stat: DepartmentStats) => !stat.acctstoptime).length / individualStats.length * 100).toFixed(1)
                        : "0"
                      }%
                    </div>
                    <div className="text-xs text-slate-600">Active Session Ratio</div>
                  </div>
                  <div className="p-4 bg-white rounded-lg border border-slate-200">
                    <div className="text-2xl font-bold text-orange-600">
                      {departments.length > 0 ?
                        (departmentOverviewData.filter(d => d.totalUsers > 0).length / departments.length * 100).toFixed(0)
                        : "0"
                      }%
                    </div>
                    <div className="text-xs text-slate-600">Department Utilization</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Alerts and Trends Section */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
              {/* System Alerts */}
              <div className="bg-gradient-to-br from-red-50 to-red-100 p-6 rounded-xl border border-red-200 shadow-lg">
                <h3 className="text-lg font-semibold mb-4 text-red-800 flex items-center gap-2">
                  <svg className="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  System Alerts
                </h3>
                <div className="space-y-3">
                  {(() => {
                    const alerts = [];

                    // Check for high error rate
                    const totalTerminated = individualStats.filter((stat: DepartmentStats) => stat.acctterminatecause && stat.acctterminatecause !== "").length;
                    const errorTerminations = individualStats.filter((stat: DepartmentStats) =>
                      stat.acctterminatecause &&
                      stat.acctterminatecause !== "" &&
                      stat.acctterminatecause !== "User-Request" &&
                      stat.acctterminatecause !== "Session-Timeout"
                    ).length;

                    if (totalTerminated > 0 && (errorTerminations / totalTerminated) > 0.1) {
                      alerts.push({
                        type: "error",
                        message: `High error rate detected: ${((errorTerminations / totalTerminated) * 100).toFixed(1)}% abnormal terminations`,
                        severity: "high"
                      });
                    }

                    // Check for departments with no activity
                    const inactiveDepts = departments.length - departmentOverviewData.filter(d => d.totalUsers > 0).length;
                    if (inactiveDepts > 0) {
                      alerts.push({
                        type: "warning",
                        message: `${inactiveDepts} department(s) have no active users`,
                        severity: "medium"
                      });
                    }

                    // Check for high bandwidth usage
                    if (activeStats.length > 0) {
                      const totalBandwidth = (activeStats[0]?.inputMbps || 0) + (activeStats[0]?.outputMbps || 0);
                      if (totalBandwidth > 100) {
                        alerts.push({
                          type: "info",
                          message: `High bandwidth usage: ${totalBandwidth.toFixed(1)} Mbps`,
                          severity: "low"
                        });
                      }
                    }

                    if (alerts.length === 0) {
                      alerts.push({
                        type: "success",
                        message: "All systems operating normally",
                        severity: "none"
                      });
                    }

                    return alerts.map((alert, index) => (
                      <div key={index} className={`p-3 rounded-lg border ${alert.severity === "high" ? "bg-red-100 border-red-300" :
                          alert.severity === "medium" ? "bg-yellow-100 border-yellow-300" :
                            alert.severity === "low" ? "bg-blue-100 border-blue-300" :
                              "bg-green-100 border-green-300"
                        }`}>
                        <div className="flex items-start gap-2">
                          <div className={`w-2 h-2 rounded-full mt-2 flex-shrink-0 ${alert.severity === "high" ? "bg-red-500" :
                              alert.severity === "medium" ? "bg-yellow-500" :
                                alert.severity === "low" ? "bg-blue-500" :
                                  "bg-green-500"
                            }`}></div>
                          <p className="text-sm text-gray-800">{alert.message}</p>
                        </div>
                      </div>
                    ));
                  })()}
                </div>
              </div>

              {/* Usage Trends */}
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-6 rounded-xl border border-blue-200 shadow-lg">
                <h3 className="text-lg font-semibold mb-4 text-blue-800 flex items-center gap-2">
                  <svg className="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
                  </svg>
                  Usage Trends
                </h3>
                <div className="space-y-4">
                  <div className="p-3 bg-white rounded-lg border border-blue-200">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-700">Staff vs Guest Usage</span>
                      <span className="text-xs text-gray-500">Current Period</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="flex-1 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-500 h-2 rounded-full"
                          style={{
                            width: `${(() => {
                              const customerUsernames = new Set(customers.map((customer: any) => customer.username));
                              const staffSessions = individualStats.filter((stat: DepartmentStats) => customerUsernames.has(stat.username)).length;
                              return individualStats.length > 0 ? (staffSessions / individualStats.length) * 100 : 0;
                            })()}%`
                          }}
                        ></div>
                      </div>
                      <span className="text-xs text-gray-600">
                        {(() => {
                          const customerUsernames = new Set(customers.map((customer: any) => customer.username));
                          const staffSessions = individualStats.filter((stat: DepartmentStats) => customerUsernames.has(stat.username)).length;
                          return individualStats.length > 0 ? ((staffSessions / individualStats.length) * 100).toFixed(0) : 0;
                        })()}% Staff
                      </span>
                    </div>
                  </div>

                  <div className="p-3 bg-white rounded-lg border border-blue-200">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-700">Department Utilization</span>
                      <span className="text-xs text-gray-500">Active/Total</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="flex-1 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-green-500 h-2 rounded-full"
                          style={{
                            width: `${departments.length > 0 ? (departmentOverviewData.filter(d => d.totalUsers > 0).length / departments.length) * 100 : 0}%`
                          }}
                        ></div>
                      </div>
                      <span className="text-xs text-gray-600">
                        {departments.length > 0 ? ((departmentOverviewData.filter(d => d.totalUsers > 0).length / departments.length) * 100).toFixed(0) : 0}%
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="bg-gradient-to-br from-green-50 to-green-100 p-6 rounded-xl border border-green-200 shadow-lg">
                <h3 className="text-lg font-semibold mb-4 text-green-800 flex items-center gap-2">
                  <svg className="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                  </svg>
                  Quick Actions
                </h3>
                <div className="space-y-3">
                  <button
                    onClick={() => router.push('/app/analytics?type=activesessions&tab=users')}
                    className="w-full p-3 bg-white rounded-lg border border-green-200 hover:bg-green-50 transition-colors text-left"
                  >
                    <div className="text-sm font-medium text-gray-800">View Active Sessions</div>
                    <div className="text-xs text-gray-600">Monitor real-time connections</div>
                  </button>

                  <button
                    onClick={() => router.push('/app/analytics?tab=department')}
                    className="w-full p-3 bg-white rounded-lg border border-green-200 hover:bg-green-50 transition-colors text-left"
                  >
                    <div className="text-sm font-medium text-gray-800">Department Analysis</div>
                    <div className="text-xs text-gray-600">Compare department performance</div>
                  </button>

                  <button
                    onClick={() => router.push('/app/analytics?tab=network')}
                    className="w-full p-3 bg-white rounded-lg border border-green-200 hover:bg-green-50 transition-colors text-left"
                  >
                    <div className="text-sm font-medium text-gray-800">Network Overview</div>
                    <div className="text-xs text-gray-600">Check network health</div>
                  </button>
                </div>
              </div>
            </div>
          </>
        )}

        {activeTab === 'users' && (
          <div className="space-y-6">
            {/* Real-time Monitoring Section for Active Sessions */}
            {activeView === "active" && activeStats.length > 0 && (
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-200 shadow-lg">
                <h2 className="text-xl font-semibold mb-4 text-blue-800 flex items-center gap-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                  Real-time Network Monitoring
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                  <div className="bg-white p-4 rounded-lg border border-blue-200 shadow-sm">
                    <div className="text-2xl font-bold text-blue-600">
                      {activeStats[0]?.count || 0}
                    </div>
                    <div className="text-sm text-blue-600">Active Users</div>
                    <div className="text-xs text-gray-500 mt-1">
                      Last updated: {activeStats[0]?.created_at ? new Date(activeStats[0].created_at).toLocaleTimeString() : "N/A"}
                    </div>
                  </div>
                  <div className="bg-white p-4 rounded-lg border border-green-200 shadow-sm">
                    <div className="text-2xl font-bold text-green-600">
                      {((activeStats[0]?.inputMbps || 0) + (activeStats[0]?.outputMbps || 0)).toFixed(1)}
                    </div>
                    <div className="text-sm text-green-600">Total Bandwidth (Mbps)</div>
                    <div className="text-xs text-gray-500 mt-1">
                      ↑ {(activeStats[0]?.inputMbps || 0).toFixed(1)} ↓ {(activeStats[0]?.outputMbps || 0).toFixed(1)}
                    </div>
                  </div>
                  <div className="bg-white p-4 rounded-lg border border-purple-200 shadow-sm">
                    <div className="text-2xl font-bold text-purple-600">
                      {activeStats[0]?.count > 0 ? (((activeStats[0]?.inputMbps || 0) + (activeStats[0]?.outputMbps || 0)) / activeStats[0].count).toFixed(2) : "0"}
                    </div>
                    <div className="text-sm text-purple-600">Avg per User (Mbps)</div>
                    <div className="text-xs text-gray-500 mt-1">
                      Real-time average
                    </div>
                  </div>
                  <div className="bg-white p-4 rounded-lg border border-orange-200 shadow-sm">
                    <div className="text-2xl font-bold text-orange-600">
                      {(() => {
                        const staffCount = activeStats.filter((stat: ActiveStat) => {
                          const customerUsernames = new Set(customers.map((customer: any) => customer.username));
                          return customerUsernames.has(stat.username);
                        }).length;
                        return `${staffCount}/${activeStats.length - staffCount}`;
                      })()}
                    </div>
                    <div className="text-sm text-orange-600">Staff/Guest Ratio</div>
                    <div className="text-xs text-gray-500 mt-1">
                      Active breakdown
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* User Analytics Summary */}
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h2 className="text-xl font-semibold mb-4">User Analytics Summary</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {(aggregatedStats.reduce((sum: number, user: DepartmentStats) => sum + (Number(user.acctinputoctets) + Number(user.acctoutputoctets)), 0) / (1024 * 1024 * 1024)).toFixed(2)} GB
                  </div>
                  <div className="text-sm text-blue-600">Total Bandwidth Usage</div>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {individualStats.length}
                  </div>
                  <div className="text-sm text-green-600">Total Sessions</div>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">
                    {aggregatedStats.length}
                  </div>
                  <div className="text-sm text-purple-600">Unique Users</div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div className="bg-white p-3 rounded-lg h-[400px] flex items-center justify-center border border-yellow-300">
                <AnalyticsBarChart data={formattedBarChartData} />
              </div>
              <div className="bg-white p-3 rounded-lg h-[400px] flex items-center justify-center border border-blue-300">
                <AnalyticsLineChartforCount data={lineChartDataforCount} startDate={startDate} endDate={endDate} />
              </div>
            </div>

            {/* View Toggle Tabs for Users */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-3">
              <div className="p-3 border-b">
                <div className="flex flex-col sm:flex-row gap-3 w-full">
                  <button
                    onClick={() => handleViewChange("aggregated")}
                    className={`px-4 py-2 rounded-full border w-full sm:w-auto h-10 ${activeView === "aggregated"
                      ? "bg-blue-600 text-white"
                      : "border-gray-300 text-gray-700 bg-white"
                      }`}
                  >
                    Aggregated View
                  </button>
                  {/* <button
                    onClick={() => handleViewChange("individual")}
                    className={`px-4 py-2 rounded-full border w-full sm:w-auto h-10 ${activeView === "individual"
                      ? "bg-blue-600 text-white"
                      : "border-gray-300 text-gray-700 bg-white"
                      }`}
                  >
                    Individual Sessions
                  </button> */}
                  <button
                    onClick={() => handleViewChange("active")}
                    className={`px-4 py-2 rounded-full border w-full sm:w-auto h-10 ${activeView === "active"
                      ? "bg-blue-600 text-white"
                      : "border-gray-300 text-gray-700 bg-white"
                      }`}
                  >
                    Active Sessions
                  </button>
                </div>
              </div>
            </div>

            {/* Filters - Hidden for Active Sessions */}
            {activeView !== "active" && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-3">
                <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-3">
                  {/* Left Section - Department Filter Only */}
                  <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 text-sm font-medium text-gray-700">
                    <span>Department:</span>
                    <select
                      value={selectedDepartment}
                      onChange={handleDepartmentChange}
                      className="border border-gray-300 rounded-md px-3 py-2 text-sm bg-white shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 min-w-[200px]"
                    >
                      <option value="">All Departments</option>
                      {departments.map((dept: PackageInfo) => (
                        <option key={dept.id} value={dept.package_name}>
                          {dept.package_name}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Right Section - Show Pages and View Info */}
                  <div className="flex flex-col lg:flex-row items-end lg:items-center gap-3">
                    {/* View Status Indicator */}
                    <div className="flex items-center gap-2">
                      <div className="text-xs text-gray-600 bg-gray-100 px-3 py-1.5 rounded-md">
                        <span className="font-medium">
                          {activeView === "aggregated"
                            ? "Aggregated"
                            : activeView === "individual"
                              ? "Individual"
                              : "Active"}{" "}
                          View
                        </span>
                        <span className="ml-2 text-blue-600 font-semibold">
                          ({currentData.length} records)
                        </span>
                      </div>
                    </div>

                    {/* Items per page */}
                    <div className="flex items-center gap-2 text-xs">
                      <span>Show</span>
                      <select
                        value={itemsPerPage}
                        onChange={handleItemsPerPageChange}
                        className="border border-gray-300 rounded-md px-2 py-1.5 text-xs focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="15">15</option>
                        <option value="30">30</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                        <option value="all">All</option>
                      </select>
                      <span>entries</span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* View Status for Active Sessions */}
            {activeView === "active" && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-3">
                <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-3">
                  <div className="text-xs text-gray-600 bg-gray-100 px-3 py-1.5 rounded-lg">
                    <span className="font-medium">Active Sessions</span>
                    <span className="ml-2 text-blue-600 font-semibold">
                      ({currentData.length} online clients)
                    </span>
                    <span className="ml-2 text-gray-500">
                      {currentData.length > 0 && (currentData[0] as any).created_at
                        ? `(${new Date(
                          (currentData[0] as any).created_at
                        ).toLocaleString()})`
                        : ""}
                    </span>
                  </div>
                </div>
              </div>
            )}

            <div className="bg-white rounded-lg shadow p-4 overflow-x-auto">
              {loading ? (
                <div className="text-center py-8 text-gray-500">
                  Loading analytics data...
                </div>
              ) : currentData?.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  No analytics data found for the selected criteria.
                </div>
              ) : (
                <div className="w-full overflow-x-auto">
                  <table className="min-w-full lg:min-w-max w-full">
                    <thead className="bg-gray-200 text-left text-xs uppercase">
                      <tr>
                        <th className="px-4 py-2">S.N.</th>
                        <th className="px-4 py-2">Username</th>
                        {(activeView === "active") && (
                          <>
                            <th className="px-4 py-2">Start Time</th>
                            {/* {activeView === "individual" && (
                              <th className="px-4 py-2">Stop Time</th>
                            )} */}
                            {activeView === "active" && (
                              <th className="px-4 py-2">Duration</th>
                            )}
                          </>
                        )}
                        {activeView === "aggregated" && (
                          <th className="px-4 py-2">Sessions</th>
                        )}

                        <th className="px-4 py-2">Upload</th>
                        <th className="px-4 py-2">Download</th>
                        {activeView === "active" && (
                          <>
                            <th className="px-4 py-2">Upload Speed (Mbps)</th>
                            <th className="px-4 py-2">Download Speed (Mbps)</th>
                          </>
                        )}
                        {activeView !== "active" && (
                          <th className="px-4 py-2">Department</th>
                        )}
                      </tr>
                    </thead>
                    <tbody>
                      {currentStats.map((item: DepartmentStats, index: number) => (
                        <tr
                          key={`${item.username}-${item.acctstarttime}-${index}`}
                          className="border-b hover:bg-gray-50"
                        >
                          <td className="px-4 py-2 text-xs">
                            {(currentPage - 1) * itemsPerPageNum + index + 1}
                          </td>
                          <td className="px-4 py-2 text-xs">
                            {activeView === "aggregated" ? (
                              <button
                                onClick={() => handleUsernameClick(item.username)}
                                className="text-blue-600 font-medium hover:text-blue-800 hover:underline cursor-pointer"
                              >
                                {item.username}
                              </button>
                            ) : (
                              <span className="text-blue-600 font-medium">
                                {item.username}
                              </span>
                            )}
                          </td>
                          {(activeView === "active") && (
                            <>
                              <td className="px-4 py-2 text-xs">
                                {new Date(item.acctstarttime).toLocaleString(
                                  "en-US",
                                  {
                                    year: "numeric",
                                    month: "2-digit",
                                    day: "2-digit",
                                    hour: "2-digit",
                                    minute: "2-digit",
                                    second: "2-digit",
                                    hour12: false,
                                  }
                                )}
                              </td>
                              {/* {activeView === "individual" && (
                                  <td className="px-4 py-2 text-xs">
                                    {item.acctstoptime
                                      ? new Date(item.acctstoptime).toLocaleString(
                                        "en-US",
                                        {
                                          year: "numeric",
                                          month: "2-digit",
                                          day: "2-digit",
                                          hour: "2-digit",
                                          minute: "2-digit",
                                          second: "2-digit",
                                          hour12: false,
                                        }
                                      )
                                      : "Active"}
                                  </td>
                                )} */}
                              {activeView === "active" && (
                                <td className="px-4 py-2 text-xs">
                                  {(() => {
                                    const startTime = new Date(item.acctstarttime);
                                    const now = new Date();
                                    const durationMinutes = Math.round(
                                      (now.getTime() - startTime.getTime()) /
                                      1000 /
                                      60
                                    );
                                    return `${durationMinutes} min`;
                                  })()}
                                </td>
                              )}
                            </>
                          )}
                          {activeView === "aggregated" && (
                            <td className="px-4 py-2 text-xs">
                              {individualStats.filter((session: DepartmentStats) => session.username === item.username).length}
                            </td>
                          )}

                          <td className="px-4 py-2 text-xs">
                            {(Number(item.acctinputoctets) / 1024 / 1024).toFixed(2)}{" "}
                            MB
                          </td>
                          <td className="px-4 py-2 text-xs">
                            {(Number(item.acctoutputoctets) / 1024 / 1024).toFixed(2)}{" "}
                            MB
                          </td>
                          {activeView === "active" && (
                            <>
                              <td className="px-4 py-2 text-xs">
                                {(item as any).inputMbps
                                  ? (item as any).inputMbps.toFixed(4)
                                  : "0.0000"}
                              </td>
                              <td className="px-4 py-2 text-xs">
                                {(item as any).outputMbps
                                  ? (item as any).outputMbps.toFixed(4)
                                  : "0.0000"}
                              </td>
                            </>
                          )}
                          {activeView !== "active" && (
                            <td className="px-4 py-2 text-xs">{item.groupname}</td>
                          )}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}

              {/* Pagination controls */}
              {totalPages > 1 && (
                <div className="flex items-center gap-1 justify-left mt-4">
                  <Button
                    className="rounded-full w-8 h-8 p-0 flex items-center justify-center"
                    size="sm"
                    onClick={() => setCurrentPage((p: number) => Math.max(p - 1, 1))}
                    disabled={currentPage === 1}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <span className="text-[12px] px-2">
                    Page {currentPage} of {totalPages}
                  </span>
                  <Button
                    className="rounded-full w-8 h-8 p-0 flex items-center justify-center"
                    size="sm"
                    onClick={() =>
                      setCurrentPage((p: number) => Math.min(p + 1, totalPages))
                    }
                    disabled={currentPage === totalPages}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              )}

              {/* User Sessions Modal */}
              {selectedUser && (
                <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4">
                  <div className="bg-white rounded-lg p-6 max-h-[90vh] overflow-y-auto w-full max-w-6xl relative">
                    <div className="flex justify-between items-center mb-4">
                      <h2 className="text-xl font-bold">
                        Individual Sessions for: {selectedUser}
                      </h2>
                      <button
                        onClick={closeUserSessionsModal}
                        className="text-gray-500 hover:text-gray-700 text-2xl font-bold"
                      >
                        ×
                      </button>
                    </div>

                    {/* Search and Controls */}
                    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 mb-4">
                      <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                        <div className="text-sm text-gray-600">
                          Total Sessions:{" "}
                          <span className="font-semibold">{userSessions.length}</span>
                        </div>

                        <input
                          type="text"
                          placeholder="Search ..."
                          value={modalSearch}
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                            setModalSearch(e.target.value);
                            setModalCurrentPage(1);
                          }}
                          className="w-full sm:w-64 px-3 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>

                      <div className="flex items-center gap-2 text-xs">
                        <span>Show</span>
                        <select
                          value={modalItemsPerPage}
                          onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {
                            setModalItemsPerPage(Number(e.target.value));
                            setModalCurrentPage(1);
                          }}
                          className="border border-gray-300 rounded px-2 py-1 text-xs focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value={10}>10</option>
                          <option value={25}>25</option>
                          <option value={50}>50</option>
                          <option value={100}>100</option>
                        </select>
                        <span>per page</span>
                      </div>
                    </div>

                    <div className="overflow-x-auto">
                      {(() => {
                        // Filter sessions based on search
                        const filteredSessions = userSessions.filter((session: DepartmentStats) => {
                          if (!modalSearch) return true;
                          const searchLower = modalSearch.toLowerCase();
                          return (
                            session.framedipaddress?.toLowerCase().includes(searchLower) ||
                            session.callingstationid?.toLowerCase().includes(searchLower) ||
                            session.acctterminatecause?.toLowerCase().includes(searchLower)
                          );
                        });

                        // Get current page sessions
                        const currentPageSessions = filteredSessions.slice(
                          (modalCurrentPage - 1) * modalItemsPerPage,
                          modalCurrentPage * modalItemsPerPage
                        );

                        return (
                          <>
                            <table className="min-w-full text-xs">
                              <thead className="bg-gray-200 text-left uppercase">
                                <tr>
                                  <th className="px-3 py-2">S.N.</th>
                                  <th className="px-3 py-2">Start Time</th>
                                  <th className="px-3 py-2">Stop Time</th>
                                  <th className="px-3 py-2">Duration</th>
                                  <th className="px-3 py-2">Department</th>
                                  <th className="px-3 py-2">Upload (MB)</th>
                                  <th className="px-3 py-2">Download (MB)</th>
                                  <th className="px-3 py-2">IP Address</th>
                                  <th className="px-3 py-2">MAC Address</th>
                                  <th className="px-3 py-2">Terminate Cause</th>
                                </tr>
                              </thead>
                              <tbody>
                                {currentPageSessions.map((session: DepartmentStats, index: number) => {
                                  const startTime = new Date(session.acctstarttime);
                                  const stopTime = session.acctstoptime
                                    ? new Date(session.acctstoptime)
                                    : null;
                                  const duration = stopTime
                                    ? Math.round(
                                      (stopTime.getTime() - startTime.getTime()) /
                                      1000 /
                                      60
                                    ) // minutes
                                    : null;

                                  return (
                                    <tr
                                      key={`${session.acctstarttime}-${index}`}
                                      className="border-b hover:bg-gray-50"
                                    >
                                      <td className="px-3 py-2">
                                        {(modalCurrentPage - 1) * modalItemsPerPage +
                                          index +
                                          1}
                                      </td>
                                      <td className="px-3 py-2">
                                        {startTime.toLocaleString("en-US", {
                                          year: "numeric",
                                          month: "2-digit",
                                          day: "2-digit",
                                          hour: "2-digit",
                                          minute: "2-digit",
                                          second: "2-digit",
                                          hour12: false,
                                        })}
                                      </td>
                                      <td className="px-3 py-2">
                                        {stopTime
                                          ? stopTime.toLocaleString("en-US", {
                                            year: "numeric",
                                            month: "2-digit",
                                            day: "2-digit",
                                            hour: "2-digit",
                                            minute: "2-digit",
                                            second: "2-digit",
                                            hour12: false,
                                          })
                                          : "Active"}
                                      </td>
                                      <td className="px-3 py-2">
                                        {duration ? `${duration} min` : "Active"}
                                      </td>
                                      <td className="px-3 py-2">{session.groupname}</td>
                                      <td className="px-3 py-2">
                                        {(
                                          Number(session.acctinputoctets) /
                                          1024 /
                                          1024
                                        ).toFixed(2)}
                                      </td>
                                      <td className="px-3 py-2">
                                        {(
                                          Number(session.acctoutputoctets) /
                                          1024 /
                                          1024
                                        ).toFixed(2)}
                                      </td>
                                      <td className="px-3 py-2">
                                        {session.framedipaddress}
                                      </td>
                                      <td className="px-3 py-2">
                                        {session.callingstationid}
                                      </td>
                                      <td className="px-3 py-2">
                                        {session.acctterminatecause || "Active"}
                                      </td>
                                    </tr>
                                  );
                                })}
                              </tbody>
                            </table>

                            {/* Modal Pagination */}
                            {Math.ceil(filteredSessions.length / modalItemsPerPage) >
                              1 && (
                                <div className="flex items-center justify-between mt-4 pt-4 border-t">
                                  <div className="text-xs text-gray-600">
                                    Showing{" "}
                                    {(modalCurrentPage - 1) * modalItemsPerPage + 1} to{" "}
                                    {Math.min(
                                      modalCurrentPage * modalItemsPerPage,
                                      filteredSessions.length
                                    )}{" "}
                                    of {filteredSessions.length} sessions
                                  </div>

                                  <div className="flex items-center gap-1">
                                    <Button
                                      className="rounded-full w-8 h-8 p-0 flex items-center justify-center"
                                      size="sm"
                                      onClick={() =>
                                        setModalCurrentPage((p: number) => Math.max(p - 1, 1))
                                      }
                                      disabled={modalCurrentPage === 1}
                                    >
                                      <ChevronLeft className="h-4 w-4" />
                                    </Button>
                                    <span className="text-xs px-2">
                                      Page {modalCurrentPage} of{" "}
                                      {Math.ceil(
                                        filteredSessions.length / modalItemsPerPage
                                      )}
                                    </span>
                                    <Button
                                      className="rounded-full w-8 h-8 p-0 flex items-center justify-center"
                                      size="sm"
                                      onClick={() =>
                                        setModalCurrentPage((p: number) =>
                                          Math.min(
                                            p + 1,
                                            Math.ceil(
                                              filteredSessions.length / modalItemsPerPage
                                            )
                                          )
                                        )
                                      }
                                      disabled={
                                        modalCurrentPage ===
                                        Math.ceil(
                                          filteredSessions.length / modalItemsPerPage
                                        )
                                      }
                                    >
                                      <ChevronRight className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </div>
                              )}
                          </>
                        );
                      })()}
                    </div>

                    <div className="mt-4 flex justify-end">
                      <button
                        onClick={closeUserSessionsModal}
                        className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
                      >
                        Close
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'network' && (
          <div className="space-y-6">
            {/* Network Overview */}
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h2 className="text-xl font-semibold mb-4">Network Performance Overview</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <div className="bg-indigo-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-indigo-600">
                    {departments.length}
                  </div>
                  <div className="text-sm text-indigo-600">Total Departments</div>
                </div>
                <div className="bg-teal-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-teal-600">
                    {activeStats.length > 0 ? activeStats[0]?.count || 0 : 0}
                  </div>
                  <div className="text-sm text-teal-600">Current Active Sessions</div>
                </div>
                <div className="bg-rose-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-rose-600">
                    {(aggregatedStats.reduce((sum: number, stat: DepartmentStats) =>
                      sum + (Number(stat.acctinputoctets) + Number(stat.acctoutputoctets)), 0) / (1024 * 1024)).toFixed(2)} MB
                  </div>
                  <div className="text-sm text-rose-600">Total Bandwidth Usage</div>
                </div>
              </div>
            </div>

            {/* Network Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h2 className="text-xl font-semibold mb-4">Data Usage Over Time</h2>
                <div className="h-[320px]">
                  <AnalyticsLineChart data={lineChartData} startDate={startDate} endDate={endDate} />
                </div>
              </div>
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h2 className="text-xl font-semibold mb-4">Active Users Count</h2>
                <div className="h-[320px]">
                  <AnalyticsLineChartforCount data={lineChartDataforCount} startDate={startDate} endDate={endDate} />
                </div>
              </div>
            </div>

            {/* Department Analytics */}
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h2 className="text-xl font-semibold mb-4">Department Analytics</h2>
              <div className="bg-white rounded-lg shadow p-4 overflow-x-auto">
                <table className="min-w-max w-full">
                  <thead className="bg-gray-200 text-left text-xs uppercase">
                    <tr>
                      <th className="px-4 py-2">S.N.</th>
                      <th className="px-4 py-2">Department</th>
                      <th className="px-4 py-2">Total Usage (GB)</th>
                      <th className="px-4 py-2">Total Staff</th>
                    </tr>
                  </thead>
                  <tbody>
                    {departments.length === 0 ? (
                      <tr>
                        <td colSpan={4} className="text-center py-8 text-gray-500">
                          No departments found.
                        </td>
                      </tr>
                    ) : (
                      sortedDepartments.map((dept: PackageInfo, index: number) => {
                        const deptStats = aggregatedStats.filter(
                          (stat: DepartmentStats) => stat.groupname === dept.package_name
                        );
                        const deptTotalUsage =
                          deptStats.reduce(
                            (sum: number, stat: DepartmentStats) =>
                              sum +
                              (Number(stat.acctinputoctets) +
                                Number(stat.acctoutputoctets)),
                            0
                          ) /
                          (1024 * 1024 * 1024); // convert to GB

                        return (
                          <tr key={dept.id} className="border-b hover:bg-gray-50 text-sm">
                            <td className="px-4 py-2">{index + 1}</td>
                            <td className="px-4 py-2">{dept.package_name}</td>
                            <td className="px-4 py-2">{deptTotalUsage.toFixed(2)}</td>
                            <td className="px-4 py-2">{deptStats.length}</td>
                          </tr>
                        );
                      })
                    )}
                  </tbody>
                </table>
              </div>

            </div>
          </div>
        )}

        {activeTab === 'department' && (
          <div className="space-y-6">
            {/* Department Overview */}
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h2 className="text-xl font-semibold mb-4">Department Overview</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div className="bg-indigo-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-indigo-600">
                    {departments.length}
                  </div>
                  <div className="text-sm text-indigo-600">Total Departments</div>
                </div>
                {/* <div className="bg-teal-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-teal-600">
                    {activeStats.length > 0 ? activeStats[0]?.count || 0 : 0}
                  </div>
                  <div className="text-sm text-teal-600">Current Active Sessions</div>
                </div> */}
                <div className="bg-rose-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-rose-600">
                    {(aggregatedStats.reduce((sum: number, stat: DepartmentStats) =>
                      sum + (Number(stat.acctinputoctets) + Number(stat.acctoutputoctets)), 0) / (1024 * 1024)).toFixed(2)} MB
                  </div>
                  <div className="text-sm text-rose-600">Total Bandwidth Usage</div>
                </div>
              </div>
            </div>

            {/* Department Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h2 className="text-xl font-semibold mb-4">Data Usage Over Time</h2>
                <div className="h-[320px]">
                  <AnalyticsLineChart data={lineChartData} startDate={startDate} endDate={endDate} />
                </div>
              </div>
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h2 className="text-xl font-semibold mb-4">Total Bandwidth Usage per Department </h2>
                <div className="bg-white p-3 rounded-lg h-[320px] flex items-center justify-center border border-purple-300">
                  <DepartmentBarChart data={formattedDepartmentBarChartData} />
                </div>
              </div>
            </div>

            {/* Department Analytics */}
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h2 className="text-xl font-semibold mb-4">Department Analytics</h2>
              <div className="bg-white rounded-lg shadow p-4 overflow-x-auto">
                <table className="min-w-max w-full">
                  <thead className="bg-gray-200 text-left text-xs uppercase">
                    <tr>
                      <th className="px-4 py-2">S.N.</th>
                      <th className="px-4 py-2">Department</th>
                      <th className="px-4 py-2">Total Usage (GB)</th>
                      <th className="px-4 py-2">Total Staff</th>
                    </tr>
                  </thead>
                  <tbody>
                    {departments.length === 0 ? (
                      <tr>
                        <td colSpan={4} className="text-center py-8 text-gray-500">
                          No departments found.
                        </td>
                      </tr>
                    ) : (
                      sortedDepartments.map((dept: PackageInfo, index: number) => {
                        const deptStats = aggregatedStats.filter(
                          (stat: DepartmentStats) => stat.groupname === dept.package_name
                        );
                        const deptTotalUsage =
                          deptStats.reduce(
                            (sum: number, stat: DepartmentStats) =>
                              sum +
                              (Number(stat.acctinputoctets) +
                                Number(stat.acctoutputoctets)),
                            0
                          ) /
                          (1024 * 1024 * 1024); // convert to GB

                        return (
                          <tr key={dept.id} className="border-b hover:bg-gray-50 text-sm">
                            <td className="px-4 py-2">{index + 1}</td>
                            <td className="px-4 py-2">{dept.package_name}</td>
                            <td className="px-4 py-2">{deptTotalUsage.toFixed(2)}</td>
                            <td className="px-4 py-2">{deptStats.length}</td>
                          </tr>
                        );
                      })
                    )}
                  </tbody>
                </table>
              </div>

            </div>
          </div>

        )}
      </div>
    </div>
  );
}